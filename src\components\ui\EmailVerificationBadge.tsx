'use client';

import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Clock } from 'lucide-react';
import { useSession } from 'next-auth/react';
import React, { useEffect, useState } from 'react';

interface EmailVerificationBadgeProps {
  className?: string;
  showText?: boolean;
}

const EmailVerificationBadge: React.FC<EmailVerificationBadgeProps> = ({
  className = '',
  showText = true
}) => {
  const { data: session } = useSession();
  const [verificationStatus, setVerificationStatus] = useState<{
    isVerified: boolean;
    isLoading: boolean;
  }>({ isVerified: false, isLoading: true });

  useEffect(() => {
    const checkVerificationStatus = async () => {
      if (!session?.user?.email) {
        setVerificationStatus({ isVerified: false, isLoading: false });
        return;
      }

      try {
        const { isEmailVerified } = await import('@/src/middleware/email-verification');
        const isVerified = await isEmailVerified(session.user.email);
        setVerificationStatus({ isVerified, isLoading: false });
      } catch (error) {
        console.error('Error checking email verification:', error);
        setVerificationStatus({ isVerified: false, isLoading: false });
      }
    };

    checkVerificationStatus();
  }, [session?.user?.email]);

  if (!session?.user?.email) {
    return null;
  }

  if (verificationStatus.isLoading) {
    return (
      <Badge variant="secondary" className={className}>
        <Clock className="mr-1 h-3 w-3" />
        {showText && 'Checking...'}
      </Badge>
    );
  }

  if (verificationStatus.isVerified) {
    return (
      <Badge variant="default" className={`bg-green-100 text-green-800 hover:bg-green-200 ${className}`}>
        <CheckCircle className="mr-1 h-3 w-3" />
        {showText && 'Verified'}
      </Badge>
    );
  }

  return (
    <Badge variant="destructive" className={className}>
      <XCircle className="mr-1 h-3 w-3" />
      {showText && 'Not Verified'}
    </Badge>
  );
};

export default EmailVerificationBadge;
