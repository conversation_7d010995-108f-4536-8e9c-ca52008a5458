import { auth } from '@/auth';
import ConfirmationPage from '@/components/auth/ConfirmationPage';
import { redirect } from '@/i18n/routing';
import { db } from '@/utils/database/drizzle';
import { VerificationCode } from '@/utils/database/schema';
import { and, desc, eq } from 'drizzle-orm';
import { getLocale } from 'next-intl/server';

const confirmationPage = async () => {
  const session = await auth();
  const locale = await getLocale();

  // Redirect if not authenticated
  if (!session || !session.user || !session.user.email) {
    return redirect({ href: '/auth/sign-in', locale });
  }

  // Check if there's a valid pending verification code for this user
  const pendingVerification = await db
    .select()
    .from(VerificationCode)
    .where(
      and(
        eq(VerificationCode.email, session.user.email),
        eq(VerificationCode.used, false)
      )
    )
    .orderBy(desc(VerificationCode.createdAt))
    .limit(1);

  // If no pending verification code exists, redirect to home
  if (!pendingVerification.length) {
    return redirect({ href: '/', locale });
  }

  // Check if the verification code is expired
  const verificationCode = pendingVerification[0];
  if (new Date() > verificationCode.expires) {
    return redirect({ href: '/', locale });
  }

  return <ConfirmationPage verificationType={verificationCode.type} />;
};

export default confirmationPage;
